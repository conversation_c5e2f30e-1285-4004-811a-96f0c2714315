'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Sidebar, 
  SidebarProvider, 
  MobileSidebarTrigger 
} from '@/components/layout/sidebar-navigation'
import { AdvancedSidebar } from '@/components/layout/sidebar-variants'
import {
  Palette,
  Eye,
  Smartphone,
  Monitor,
  Tablet,
  Settings,
  Star,
  Zap,
  Sparkles,
  Crown,
  Gem,
  Rocket,
  Layers,
  Cpu,
  Wand2,
  Lightbulb
} from 'lucide-react'

const basicVariants = [
  {
    key: 'default',
    name: 'Classic White',
    description: 'Clean and professional white sidebar',
    icon: Monitor,
    color: 'bg-white',
    category: 'basic'
  },
  {
    key: 'modern',
    name: 'Dark Modern',
    description: 'Sleek dark theme for modern apps',
    icon: Zap,
    color: 'bg-slate-900',
    category: 'basic'
  },
  {
    key: 'minimal',
    name: '<PERSON><PERSON> Gray',
    description: 'Subtle gray theme that stays minimal',
    icon: Sparkles,
    color: 'bg-gray-50',
    category: 'basic'
  },
  {
    key: 'glass',
    name: 'Glass Morphism',
    description: 'Modern glass effect with backdrop blur',
    icon: Gem,
    color: 'bg-white/80',
    category: 'basic'
  },
  {
    key: 'gradient',
    name: 'Brand Gradient',
    description: 'Beautiful gradient with brand colors',
    icon: Crown,
    color: 'bg-gradient-to-b from-orange-500 to-red-600',
    category: 'basic'
  }
]

const advancedVariants = [
  {
    key: 'neon',
    name: 'Neon Cyber',
    description: 'Futuristic neon theme with glowing effects',
    icon: Lightbulb,
    color: 'bg-black',
    category: 'advanced'
  },
  {
    key: 'floating',
    name: 'Floating Card',
    description: 'Elevated floating design with rounded corners',
    icon: Layers,
    color: 'bg-white/95',
    category: 'advanced'
  },
  {
    key: 'compact',
    name: 'Compact Pro',
    description: 'Space-efficient design for power users',
    icon: Cpu,
    color: 'bg-slate-800',
    category: 'advanced'
  },
  {
    key: 'premium',
    name: 'Premium Suite',
    description: 'Feature-rich premium experience',
    icon: Crown,
    color: 'bg-gradient-to-b from-slate-900 to-slate-800',
    category: 'advanced'
  },
  {
    key: 'animated',
    name: 'Animated Magic',
    description: 'Smooth animations and micro-interactions',
    icon: Wand2,
    color: 'bg-white',
    category: 'advanced'
  }
]

export default function AdvancedSidebarDemoPage() {
  const [selectedVariant, setSelectedVariant] = useState('default')
  const [selectedCategory, setSelectedCategory] = useState<'basic' | 'advanced'>('basic')
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  const allVariants = [...basicVariants, ...advancedVariants]
  const currentVariants = selectedCategory === 'basic' ? basicVariants : advancedVariants

  const viewModeClasses = {
    desktop: 'w-full max-w-7xl',
    tablet: 'w-full max-w-4xl',
    mobile: 'w-full max-w-sm'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-4">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <Rocket className="h-8 w-8 text-blue-600" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Advanced Sidebar Gallery
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover 10 stunning sidebar designs ranging from classic to cutting-edge. 
            Each variant is crafted for different use cases and aesthetic preferences.
          </p>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto">
            <TabsTrigger value="basic" className="flex items-center space-x-2">
              <Monitor className="h-4 w-4" />
              <span>Basic Collection</span>
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center space-x-2">
              <Rocket className="h-4 w-4" />
              <span>Advanced Collection</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Basic Collection</h2>
              <p className="text-gray-600">Essential sidebar designs for everyday use</p>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Advanced Collection</h2>
              <p className="text-gray-600">Cutting-edge designs with premium features</p>
            </div>
          </TabsContent>
        </Tabs>

        {/* Variant Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {currentVariants.map((variant) => {
            const Icon = variant.icon
            const isSelected = selectedVariant === variant.key
            
            return (
              <Card 
                key={variant.key}
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
                  isSelected ? 'ring-2 ring-blue-500 shadow-lg scale-105' : ''
                }`}
                onClick={() => setSelectedVariant(variant.key)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-2">
                    <div className={`h-10 w-10 rounded-xl ${variant.color} flex items-center justify-center border shadow-sm`}>
                      <Icon className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-sm">{variant.name}</CardTitle>
                      {isSelected && (
                        <Badge variant="default" className="text-xs mt-1">
                          Active
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <CardDescription className="text-xs">
                    {variant.description}
                  </CardDescription>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* View Mode Controls */}
        <div className="flex items-center justify-center space-x-4">
          <span className="text-sm font-medium text-gray-700">Preview Size:</span>
          <div className="flex items-center space-x-2">
            {[
              { key: 'desktop', icon: Monitor, label: 'Desktop' },
              { key: 'tablet', icon: Tablet, label: 'Tablet' },
              { key: 'mobile', icon: Smartphone, label: 'Mobile' }
            ].map((mode) => {
              const Icon = mode.icon
              return (
                <Button
                  key={mode.key}
                  variant={viewMode === mode.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode(mode.key as any)}
                  className="flex items-center space-x-1"
                >
                  <Icon className="h-4 w-4" />
                  <span>{mode.label}</span>
                </Button>
              )
            })}
          </div>
        </div>

        {/* Live Preview */}
        <Card className="overflow-hidden shadow-xl">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Eye className="h-5 w-5" />
                  <span>Live Interactive Preview</span>
                </CardTitle>
                <CardDescription>
                  Experience the {allVariants.find(v => v.key === selectedVariant)?.name} sidebar in action
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {selectedVariant}
                </Badge>
                <Badge variant={selectedCategory === 'basic' ? 'default' : 'secondary'} className="text-xs">
                  {selectedCategory}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="bg-gray-100 p-4 flex justify-center">
              <div className={`${viewModeClasses[viewMode]} bg-white rounded-lg shadow-2xl overflow-hidden`}>
                <SidebarProvider>
                  <div className="flex h-96">
                    {selectedCategory === 'basic' ? (
                      <Sidebar variant={selectedVariant as any} />
                    ) : (
                      <AdvancedSidebar variant={selectedVariant as any} />
                    )}
                    <div className="flex-1 p-6 bg-gradient-to-br from-gray-50 to-gray-100">
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
                          <p className="text-gray-600">
                            {selectedCategory === 'advanced' ? 'Advanced' : 'Standard'} sidebar experience
                          </p>
                        </div>
                        <MobileSidebarTrigger />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="shadow-sm">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                              <Sparkles className="h-5 w-5 text-blue-500" />
                              <span>Features</span>
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="text-sm text-gray-600 space-y-2">
                              <li className="flex items-center space-x-2">
                                <Star className="h-3 w-3 text-yellow-500" />
                                <span>Fully responsive design</span>
                              </li>
                              <li className="flex items-center space-x-2">
                                <Star className="h-3 w-3 text-yellow-500" />
                                <span>Smooth animations</span>
                              </li>
                              <li className="flex items-center space-x-2">
                                <Star className="h-3 w-3 text-yellow-500" />
                                <span>Mobile-friendly overlay</span>
                              </li>
                              <li className="flex items-center space-x-2">
                                <Star className="h-3 w-3 text-yellow-500" />
                                <span>Collapsible navigation</span>
                              </li>
                            </ul>
                          </CardContent>
                        </Card>
                        
                        <Card className="shadow-sm">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                              <Settings className="h-5 w-5 text-green-500" />
                              <span>Customization</span>
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-gray-600 mb-3">
                              This sidebar variant offers:
                            </p>
                            <div className="flex flex-wrap gap-1">
                              <Badge variant="secondary" className="text-xs">Theming</Badge>
                              <Badge variant="secondary" className="text-xs">Icons</Badge>
                              <Badge variant="secondary" className="text-xs">Colors</Badge>
                              <Badge variant="secondary" className="text-xs">Layout</Badge>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                </SidebarProvider>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Implementation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Quick Implementation</span>
            </CardTitle>
            <CardDescription>
              Copy and paste this code to use the {allVariants.find(v => v.key === selectedVariant)?.name} sidebar
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm">
                <code>{`import { SidebarProvider, ${selectedCategory === 'basic' ? 'Sidebar' : 'AdvancedSidebar'} } from '@/components/layout/sidebar-navigation'

export default function Layout({ children }) {
  return (
    <SidebarProvider>
      <div className="flex h-screen">
        <${selectedCategory === 'basic' ? 'Sidebar' : 'AdvancedSidebar'} variant="${selectedVariant}" />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </SidebarProvider>
  )
}`}</code>
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
