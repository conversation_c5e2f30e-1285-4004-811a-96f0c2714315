'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { Role, Permission, UserRole } from '@/types/roles';
import { hasPermission, canPerformAction } from '@/lib/auth/permissions';

interface RoleContextType {
  roles: Role[];
  userRoles: UserRole[];
  userPermissions: Permission[];
  loading: boolean;
  error: string | null;
  
  // Permission checking functions
  hasPermission: (permission: Permission) => boolean;
  canPerformAction: (resource: string, action: string, scope?: string) => boolean;
  
  // Role management functions
  fetchRoles: () => Promise<void>;
  createRole: (roleData: { name: string; description?: string; permissions: Permission[] }) => Promise<Role>;
  updateRole: (roleId: string, updates: Partial<Role>) => Promise<Role>;
  deleteRole: (roleId: string) => Promise<void>;
  
  // User role management
  assignRole: (userId: string, roleId: string) => Promise<void>;
  removeRole: (userId: string, roleId: string) => Promise<void>;
  fetchUserRoles: () => Promise<void>;
}

const RoleContext = createContext<RoleContextType | undefined>(undefined);

interface RoleProviderProps {
  children: React.ReactNode;
  organizationId: string;
  userId: string;
}

export function RoleProvider({ children, organizationId, userId }: RoleProviderProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch roles from API
  const fetchRoles = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/roles?organizationId=${organizationId}`);
      if (!response.ok) throw new Error('Failed to fetch roles');
      
      const data = await response.json();
      setRoles(data.roles);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch roles');
    } finally {
      setLoading(false);
    }
  }, [organizationId]);

  // Fetch user roles and permissions
  const fetchUserRoles = useCallback(async () => {
    try {
      const response = await fetch(`/api/users/${userId}/roles?organizationId=${organizationId}`);
      if (!response.ok) throw new Error('Failed to fetch user roles');
      
      const data = await response.json();
      setUserRoles(data.userRoles);
      
      // Extract permissions from roles
      const permissions: Permission[] = [];
      data.userRoles.forEach((userRole: UserRole) => {
        if (userRole.role) {
          permissions.push(...userRole.role.permissions);
        }
      });
      
      // Remove duplicates
      const uniquePermissions = permissions.filter((permission, index) => {
        return permissions.findIndex(p => 
          p.resource === permission.resource && 
          p.action === permission.action &&
          p.scope === permission.scope
        ) === index;
      });
      
      setUserPermissions(uniquePermissions);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user roles');
    }
  }, [userId, organizationId]);

  // Create a new role
  const createRole = async (roleData: { name: string; description?: string; permissions: Permission[] }): Promise<Role> => {
    const response = await fetch('/api/roles', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ...roleData, organizationId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create role');
    }

    const data = await response.json();
    setRoles(prev => [...prev, data.role]);
    return data.role;
  };

  // Update a role
  const updateRole = async (roleId: string, updates: Partial<Role>): Promise<Role> => {
    const response = await fetch(`/api/roles/${roleId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ...updates, organizationId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update role');
    }

    const data = await response.json();
    setRoles(prev => prev.map(role => role.id === roleId ? data.role : role));
    return data.role;
  };

  // Delete a role
  const deleteRole = async (roleId: string): Promise<void> => {
    const response = await fetch(`/api/roles/${roleId}?organizationId=${organizationId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete role');
    }

    setRoles(prev => prev.filter(role => role.id !== roleId));
  };

  // Assign role to user
  const assignRole = async (targetUserId: string, roleId: string): Promise<void> => {
    const response = await fetch('/api/user-roles', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: targetUserId, roleId, organizationId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to assign role');
    }

    // Refresh user roles if we're updating the current user
    if (targetUserId === userId) {
      await fetchUserRoles();
    }
  };

  // Remove role from user
  const removeRole = async (targetUserId: string, roleId: string): Promise<void> => {
    const response = await fetch('/api/user-roles', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: targetUserId, roleId, organizationId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to remove role');
    }

    // Refresh user roles if we're updating the current user
    if (targetUserId === userId) {
      await fetchUserRoles();
    }
  };

  // Permission checking functions
  const checkPermission = (permission: Permission): boolean => {
    return hasPermission(userPermissions, permission);
  };

  const checkAction = (resource: string, action: string, scope?: string): boolean => {
    return canPerformAction(userPermissions, resource, action, scope);
  };

  // Load initial data
  useEffect(() => {
    if (organizationId && userId) {
      Promise.all([fetchRoles(), fetchUserRoles()]);
    }
  }, [organizationId, userId, fetchRoles, fetchUserRoles]);

  const value: RoleContextType = {
    roles,
    userRoles,
    userPermissions,
    loading,
    error,
    hasPermission: checkPermission,
    canPerformAction: checkAction,
    fetchRoles,
    createRole,
    updateRole,
    deleteRole,
    assignRole,
    removeRole,
    fetchUserRoles,
  };

  return (
    <RoleContext.Provider value={value}>
      {children}
    </RoleContext.Provider>
  );
}

export function useRoles() {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error('useRoles must be used within a RoleProvider');
  }
  return context;
} 